import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const questionBankTimeStampSeed = async () => {
  try {
    const updatedRecords = await prisma.questionBank.updateMany({
      where: {
        OR: [
          { createdAt: null },
          { updatedAt: null },
        ],
      },
      data: {
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

  } catch (error) {
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

questionBankTimeStampSeed();