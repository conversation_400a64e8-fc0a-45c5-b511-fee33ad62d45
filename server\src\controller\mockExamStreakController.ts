import { Request, Response } from "express";
import * as mockExamStreakServices from "../services/mockExamStreakServices";

export const getMockExamStreakController = async (req: Request,res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const streakData = await mockExamStreakServices.getCurrentStreak(studentId);
    return res.status(200).json({
      success: true,
      data: streakData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const updateMockExamStreakController = async (req: Request,res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const streakData = await mockExamStreakServices.updateStreak(studentId);
    return res.status(200).json({
      success: true,
      data: streakData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};