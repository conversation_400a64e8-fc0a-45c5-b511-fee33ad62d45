import prisma from '../config/prismaClient';
import axios from 'axios';

interface Ranking {
  studentId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  score: number;
  attempts: number;
  rank: number;
  contact?: string;
  totalQuestions: number;
}

export const calculateRankings = async (
  examId: number,
  page: number = 1,
  limit: number = 10,
  filters: {
    firstName?: string,
    lastName?: string,
    email?: string,
    score?: string,
    rank?: string,
    search?: string
    contact?: string
  } = {}
): Promise<any> => {
  try {
    if (page < 1) throw new Error('Page number must be at least 1');
    if (limit < 1) throw new Error('Limit must be at least 1');
    const validLimits = [10, 20, 50, 200, 500];
    const validatedLimit = validLimits.includes(limit) ? limit : 10;

    const answers = await prisma.saveExamAnswer.findMany({
      where: { examId },
      include: {
        questionBank: {
          select: { correctAnswer: true },
        },
      },
    });

    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      select: { total_questions: true },
    });

    if (!exam || !exam.total_questions) {
      throw new Error('Exam not found or invalid total_questions');
    }
    const totalQuestions = exam.total_questions;

    if (!answers.length) {
      return { rankings: [], totalItems: 0, totalPages: 0, currentPage: page };
    }

    const scoreMap: { [studentId: string]: { score: number; attempts: number } } = {};
    for (const answer of answers) {
      if (!answer.studentId) {
        continue;
      }
      if (!scoreMap[answer.studentId]) {
        scoreMap[answer.studentId] = { score: 0, attempts: 0 };
      }
      scoreMap[answer.studentId].attempts++;
      if (
        answer.selectedAns &&
        answer.questionBank?.correctAnswer &&
        answer.selectedAns === answer.questionBank.correctAnswer
      ) {
        scoreMap[answer.studentId].score++;
      }
    }

    const scoreArray = Object.entries(scoreMap)
      .map(([studentId, { score, attempts }]) => ({
        studentId,
        score,
        attempts,
      }))
      .sort((a, b) => {
        if (a.score !== b.score) return b.score - a.score;
        return a.attempts - b.attempts;
      });

    if (!scoreArray.length) {
      return { rankings: [], totalItems: 0, totalPages: 0, currentPage: page };
    }

    const rankings: Ranking[] = [];
    let currentRank = 1;
    let studentsWithSameRank = 0;

    for (let i = 0; i < scoreArray.length; i++) {
      const { studentId, score, attempts } = scoreArray[i];

      if (i > 0) {
        const prevStudent = scoreArray[i - 1];
        if (score !== prevStudent.score || attempts !== prevStudent.attempts) {
          currentRank += studentsWithSameRank + 1;
          studentsWithSameRank = 0;
        } else {
          studentsWithSameRank++;
        }
      }

      rankings.push({
        studentId,
        score,
        attempts,
        rank: currentRank,
        totalQuestions
      });
    }

    const applicantIds = rankings.map((r) => r.studentId);
    let studentData: any[] = [];
    try {
      const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`, {
        applicantIds,
      });
      studentData = response.data;
    } catch (error) {
      console.error(`Error fetching student data for exam ${examId}:`, error);
    }

    let enrichedRankings = rankings.map((ranking) => {
      const student = studentData.find((s: any) => s.id === ranking.studentId);
      return {
        ...ranking,
        firstName: student?.firstName || 'Unknown',
        lastName: student?.lastName || '',
        email: student?.email || 'N/A',
        contact: student?.contact || 'N/A'
      };
    });

    if (filters.firstName && filters.firstName.trim()) {
      const filterTerm = filters.firstName.toLowerCase().trim();
      enrichedRankings = enrichedRankings.filter((student: any) =>
        student.firstName?.toLowerCase().includes(filterTerm)
      );
    }

    if (filters.lastName && filters.lastName.trim()) {
      const filterTerm = filters.lastName.toLowerCase().trim();
      enrichedRankings = enrichedRankings.filter((student: any) =>
        student.lastName?.toLowerCase().includes(filterTerm)
      );
    }
    if (filters.contact && filters.contact.trim()) {
      const filterTerm = filters.contact.toLowerCase().trim();
      enrichedRankings = enrichedRankings.filter((student: any) =>
        student.contact?.toLowerCase().includes(filterTerm)
      );
    }

    if (filters.email && filters.email.trim()) {
      const filterTerm = filters.email.toLowerCase().trim();
      enrichedRankings = enrichedRankings.filter((student: any) =>
        student.email?.toLowerCase().includes(filterTerm)
      );
    }
    if (filters.score && filters.score.trim()) {
      const scoreFilter = filters.score.trim();
      if (scoreFilter.includes('-')) {
        const [minScore, maxScore] = scoreFilter.split('-').map(s => parseInt(s.trim()));
        if (!isNaN(minScore) && !isNaN(maxScore)) {
          enrichedRankings = enrichedRankings.filter((student: any) =>
            student.score >= minScore && student.score <= maxScore
          );
        }
      } else if (!isNaN(parseInt(scoreFilter))) {
        const exactScore = parseInt(scoreFilter);
        enrichedRankings = enrichedRankings.filter((student: any) =>
          student.score === exactScore
        );
      } else {
        enrichedRankings = enrichedRankings.filter((student: any) =>
          student.score.toString().includes(scoreFilter)
        );
      }
    }
    if (filters.rank && filters.rank.trim()) {
      const rankFilter = filters.rank.trim();
      if (rankFilter.includes('-')) {
        const [minRank, maxRank] = rankFilter.split('-').map(r => parseInt(r.trim()));
        if (!isNaN(minRank) && !isNaN(maxRank)) {
          enrichedRankings = enrichedRankings.filter((student: any) =>
            student.rank >= minRank && student.rank <= maxRank
          );
        }
      } else if (!isNaN(parseInt(rankFilter))) {
        const exactRank = parseInt(rankFilter);
        enrichedRankings = enrichedRankings.filter((student: any) =>
          student.rank === exactRank
        );
      } else {
        enrichedRankings = enrichedRankings.filter((student: any) =>
          student.rank.toString().includes(rankFilter)
        );
      }
    }
    const totalItems = enrichedRankings.length;
    const totalPages = Math.ceil(totalItems / validatedLimit);
    const startIndex = (page - 1) * validatedLimit;
    const paginatedRankings = enrichedRankings.slice(startIndex, startIndex + validatedLimit);

    return {
      rankings: paginatedRankings,
      totalItems,
      totalPages,
      currentPage: page,
    };
  } catch (error: any) {
    console.error(`Error calculating rankings for exam ${examId}:`, error.message);
    throw new Error(`Failed to calculate rankings: ${error.message}`);
  }
};