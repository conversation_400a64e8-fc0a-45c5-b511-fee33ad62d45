import { z } from "zod";

const MediumEnum = z.enum(["GUJARATI", "ENGLISH"]);
const LevelEnum = z.enum(["EASY", "MEDIUM", "HARD"]);

export const createQuestionSchema = z.object({
  question: z.string().min(1, "Question can't be empty"),
  optionOne: z.string().min(1, "Option 1 is required"),
  optionTwo: z.string().min(1, "Option 2 is required"),
  optionThree: z.string().min(1, "Option 3 is required"),
  optionFour: z.string().min(1, "Option 4 is required"),
  correctAnswer: z.string().min(1, "Correct answer is required"),
  medium:MediumEnum,
  standard: z.string().min(1, "Standard is required"),
  subject: z.string().min(1, "Subject is required"),
  level: LevelEnum,
    classID: z.string().optional(),
  chapter: z.string().optional(),
});

export const upadateQuestionSchema = createQuestionSchema.partial();