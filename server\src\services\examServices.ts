import prisma from '../config/prismaClient';
import { Exam } from '@prisma/client';

// Create a new exam
export const createExam = async (examData: Exam): Promise<Exam> => {
  return prisma.exam.create({
    data: examData,
  });
};

// Get all exams
export const getAllExams = async (
  page: number = 1,
  limit: number = 10,
  applicantId?: string
): Promise<any> => {
  const safePage = Math.max(1, page);
  const safeLimit = Math.max(1, Math.min(100, limit));
  const skip = (safePage - 1) * safeLimit;

  const [exams, total] = await Promise.all([
    prisma.exam.findMany({
      skip,
      take: safeLimit,
      orderBy: { createdAt: 'desc' },
      include: {
        UwhizPriceRank: {
          select: {
            id: true,
            rank: true,
            price: true,
          },
          where: { rank: 1 },
        },
        _count: {
          select: {
            examApplication: true,
          },
        },
        examApplication: applicantId
          ? {
              where: { applicantId },
              select: { id: true },
            }
          : false,
      },
    }),
    prisma.exam.count(),
  ]);

  const transformedExams = exams.map((exam) => ({
    ...exam,
    totalApplicants: exam._count.examApplication,
    hasApplied: applicantId ? exam.examApplication.length > 0 : false,
    isMaxLimitReached: exam._count.examApplication >= exam.total_student_intake,
  }));

  return {
    exams: transformedExams,
    total,
    currentPage: safePage,
    totalPages: Math.ceil(total / safeLimit),
  };
};

export const getExamById = async (id: number, applicantId?: string): Promise<any> => {
  const exam = await prisma.exam.findUnique({
    where: { id },
    include: {
      UwhizPriceRank: {
        select: {
          price: true,
        },
        where: { rank: 1 },
      },
      _count: {
        select: {
          examApplication: true,
        },
      },
      examApplication: applicantId
        ? {
            where: { applicantId },
            select: { id: true },
          }
        : false,
    },
  });

  if (!exam) return null;

  return {
    ...exam,
    totalApplicants: exam._count.examApplication,
    hasApplied: applicantId ? exam.examApplication.length > 0 : false,
    isMaxLimitReached: exam._count.examApplication >= exam.total_student_intake,
  };
};

export const updateExam = async (id: number, data: Partial<any>): Promise<Exam> => {
  return prisma.exam.update({
    where: { id },
    data,
  });
};

// Delete an exam
export const deleteExam = async (id: number): Promise<Exam> => {
  try {
    return await prisma.exam.delete({
      where: { id },
    });
  } catch (error: any) {
    throw new Error(error.message || 'Failed to delete exam');
  }
};

export function countExams(): any {
  throw new Error('Function not implemented.');
}
