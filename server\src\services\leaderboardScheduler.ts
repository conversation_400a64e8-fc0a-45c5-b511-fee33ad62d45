import * as cron from 'node-cron';
import prisma from '@/config/prismaClient';
import { processPurchase } from '@/modules/client/services/storePurchaseService';

interface LeaderboardWinner {
  rank: number;
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount: number;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
}

interface BookItem {
  id: string;
  name: string;
  coinPrice: number;
}

// Get daily leaderboard winners (top 3)
const getDailyLeaderboardWinners = async (): Promise<LeaderboardWinner[]> => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // Get mock exam results for today
    const todayResults = await prisma.mockExamResult.findMany({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
      include: {
        student: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: [
        { score: 'desc' },
        { createdAt: 'asc' }, // Earlier submission wins in case of tie
      ],
      take: 3, // Get top 3
    });

    // Transform to leaderboard format
    const winners: LeaderboardWinner[] = todayResults.map((result, index) => ({
      rank: index + 1,
      studentId: result.studentId,
      score: result.score,
      coinEarnings: result.coinEarnings || 0,
      streakCount: 0, // We'll calculate this separately if needed
      firstName: result.student?.firstName || null,
      lastName: result.student?.lastName || null,
      email: result.student?.email || null,
    }));

    return winners;
  } catch (error) {
    console.error('Error fetching daily leaderboard winners:', error);
    return [];
  }
};

// Get available book items from store
const getAvailableBooks = async (): Promise<BookItem[]> => {
  try {
    const books = await prisma.storeItem.findMany({
      where: {
        category: 'Books',
        status: 'ACTIVE',
        availableStock: {
          gt: 0,
        },
      },
      select: {
        id: true,
        name: true,
        coinPrice: true,
      },
      orderBy: {
        coinPrice: 'asc', // Start with cheaper books
      },
    });

    return books;
  } catch (error) {
    console.error('Error fetching available books:', error);
    return [];
  }
};

// Check if user has sufficient coins
const checkUserCoins = async (studentId: string): Promise<number> => {
  try {
    const userCoins = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId: studentId,
          modelType: 'STUDENT',
        },
      },
    });

    return userCoins?.coins || 0;
  } catch (error) {
    console.error('Error checking user coins:', error);
    return 0;
  }
};

// Check if user already received a book today
const hasReceivedBookToday = async (studentId: string): Promise<boolean> => {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const existingOrder = await prisma.storeOrder.findFirst({
      where: {
        modelId: studentId,
        modelType: 'STUDENT',
        createdAt: {
          gte: startOfDay,
          lt: endOfDay,
        },
        item: {
          category: 'Books',
        },
      },
      include: {
        item: true,
      },
    });

    return !!existingOrder;
  } catch (error) {
    console.error('Error checking if user received book today:', error);
    return false;
  }
};

// Place automatic book order for winner
const placeAutomaticBookOrder = async (winner: LeaderboardWinner, rank: number): Promise<boolean> => {
  try {
    console.log(`Processing automatic book order for rank ${rank} winner: ${winner.firstName} ${winner.lastName} (${winner.studentId})`);

    // Check if already received a book today
    const alreadyReceived = await hasReceivedBookToday(winner.studentId);
    if (alreadyReceived) {
      console.log(`Winner ${winner.studentId} already received a book today, skipping...`);
      return false;
    }

    // Get available books
    const availableBooks = await getAvailableBooks();
    if (availableBooks.length === 0) {
      console.log('No books available in store, skipping automatic order...');
      return false;
    }

    // Check user's coin balance
    const userCoins = await checkUserCoins(winner.studentId);
    
    // Find a book the user can afford
    const affordableBook = availableBooks.find(book => book.coinPrice <= userCoins);
    
    if (!affordableBook) {
      console.log(`Winner ${winner.studentId} doesn't have enough coins for any available book (has ${userCoins} coins)`);
      return false;
    }

    // Prepare purchase data
    const purchaseData = {
      userId: winner.studentId,
      userType: 'STUDENT' as const,
      cartItems: [
        {
          id: affordableBook.id,
          name: affordableBook.name,
          coinPrice: affordableBook.coinPrice,
          quantity: 1,
        },
      ],
      totalCoins: affordableBook.coinPrice,
    };

    // Process the purchase
    const result = await processPurchase(purchaseData);

    if (result.success) {
      console.log(`✅ Successfully placed automatic book order for rank ${rank} winner: ${winner.firstName} ${winner.lastName}`);
      console.log(`   Book: ${affordableBook.name} (${affordableBook.coinPrice} coins)`);
      console.log(`   Order ID: ${result.orderId}`);
      return true;
    } else {
      console.error(`❌ Failed to place automatic book order for ${winner.studentId}:`, result.error);
      return false;
    }
  } catch (error) {
    console.error(`Error placing automatic book order for winner ${winner.studentId}:`, error);
    return false;
  }
};

// Main function to process daily winners
const processDailyWinners = async (): Promise<void> => {
  try {
    console.log('🏆 Starting daily leaderboard winner processing...');
    
    const winners = await getDailyLeaderboardWinners();
    
    if (winners.length === 0) {
      console.log('No winners found for today');
      return;
    }

    console.log(`Found ${winners.length} winners for today:`);
    winners.forEach((winner, index) => {
      console.log(`  ${index + 1}. ${winner.firstName} ${winner.lastName} (Score: ${winner.score})`);
    });

    // Process each winner
    const results = await Promise.allSettled(
      winners.map((winner, index) => placeAutomaticBookOrder(winner, index + 1))
    );

    // Log results
    let successCount = 0;
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        successCount++;
      } else if (result.status === 'rejected') {
        console.error(`Failed to process winner ${index + 1}:`, result.reason);
      }
    });

    console.log(`✅ Daily winner processing completed. ${successCount}/${winners.length} orders placed successfully.`);
  } catch (error) {
    console.error('Error in processDailyWinners:', error);
  }
};

// Initialize the scheduler
export const initializeLeaderboardScheduler = (): void => {
  console.log('🕐 Initializing leaderboard scheduler...');
  
  // Schedule to run every day at 11:59 PM (23:59)
  // This ensures we capture the final leaderboard for the day
  cron.schedule('59 23 * * *', async () => {
    console.log('⏰ Daily leaderboard scheduler triggered at 11:59 PM');
    await processDailyWinners();
  }, {
    scheduled: true,
    timezone: 'Asia/Kolkata', // Adjust timezone as needed
  });

  // Optional: Schedule for testing - runs every minute (comment out in production)
  // cron.schedule('* * * * *', async () => {
  //   console.log('🧪 Test scheduler triggered');
  //   await processDailyWinners();
  // });

  console.log('✅ Leaderboard scheduler initialized successfully');
  console.log('   - Daily processing: Every day at 11:59 PM IST');
};

// Export for manual testing
export { processDailyWinners };
