import { z } from "zod";

//Exam Input Schema
export const examSchema = z.object({
  exam_name: z
    .string()
    .min(1, "Exam name is required")
    .max(100, "Exam name must be 100 characters or less"),
  start_date: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid start date",
    })
    .refine((val) => new Date(val) >= new Date(), {
      message: "Start date must be in the future",
    }),
  duration: z
    .number()
    .min(1, "Duration must be at least 1 minute")
    .max(1440, "Duration must be less than 24 hours"),
  marks: z
    .number()
    .min(0, "Marks cannot be negative")
    .max(1000, "Marks must be less than 1000"),
  level: z.enum(["easy", "medium", "hard"], {
    errorMap: () => ({ message: "Please select a valid level" }),
  }),
  total_student_intake: z
    .number()
    .min(1, "At least 1 student must be allowed")
    .max(10000, "Student intake must be less than 10,000"),
  total_questions: z
    .number()
    .min(1, "At least 1 question is required")
    .max(1000, "Total questions must be less than 1000"),

  coins_required: z
    .number()
    .min(0, "Coins required cannot be negative")
    .max(10000, "Coins required must be less than 10,000"),
  exam_type: z.enum(["CLASSES", "STUDENTS"]).optional(),
  start_registration_date: z
    .string()
    .refine((val) => !isNaN(Date.parse(val)), {
      message: "Invalid start date",
    })
    .refine((val) => new Date(val) >= new Date(), {
      message: "Start date must be in the future",
    }).optional(),
});

export type ExamFormValues = z.infer<typeof examSchema>;

// Schema for Partial Updates (While Updating Data)
export const examUpdateSchema = examSchema.partial();
