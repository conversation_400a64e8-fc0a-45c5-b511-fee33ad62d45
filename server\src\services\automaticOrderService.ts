import prisma from '@/config/prismaClient';
import { processPurchase } from '@/modules/client/services/storePurchaseService';

interface AutoOrderConfig {
  rank1BookId?: string; // Specific book for 1st place
  rank2BookId?: string; // Specific book for 2nd place  
  rank3BookId?: string; // Specific book for 3rd place
  fallbackToAffordable: boolean; // If specific book not available, use affordable book
  maxCoinPrice: number; // Maximum coin price for automatic orders
}

interface OrderResult {
  success: boolean;
  orderId?: string;
  bookName?: string;
  coinPrice?: number;
  error?: string;
}

// Default configuration for automatic orders
const DEFAULT_CONFIG: AutoOrderConfig = {
  fallbackToAffordable: true,
  maxCoinPrice: 500, // Maximum 500 coins for automatic orders
};

// Get configuration from database or use defaults
const getAutoOrderConfig = async (): Promise<AutoOrderConfig> => {
  try {
    // You can store this configuration in a database table if needed
    // For now, using defaults
    return DEFAULT_CONFIG;
  } catch (error) {
    console.error('Error fetching auto order config:', error);
    return DEFAULT_CONFIG;
  }
};

// Get specific book for rank or find affordable alternative
const getBookForRank = async (rank: number, userCoins: number, config: AutoOrderConfig): Promise<any> => {
  try {
    let specificBookId: string | undefined;
    
    // Get specific book ID based on rank
    switch (rank) {
      case 1:
        specificBookId = config.rank1BookId;
        break;
      case 2:
        specificBookId = config.rank2BookId;
        break;
      case 3:
        specificBookId = config.rank3BookId;
        break;
    }

    // Try to get the specific book first
    if (specificBookId) {
      const specificBook = await prisma.storeItem.findFirst({
        where: {
          id: specificBookId,
          category: 'Books',
          status: 'ACTIVE',
          availableStock: { gt: 0 },
          coinPrice: { lte: Math.min(userCoins, config.maxCoinPrice) },
        },
      });

      if (specificBook) {
        return specificBook;
      }
    }

    // Fallback to affordable book if enabled
    if (config.fallbackToAffordable) {
      const affordableBook = await prisma.storeItem.findFirst({
        where: {
          category: 'Books',
          status: 'ACTIVE',
          availableStock: { gt: 0 },
          coinPrice: { lte: Math.min(userCoins, config.maxCoinPrice) },
        },
        orderBy: [
          { coinPrice: 'desc' }, // Get the most expensive book they can afford
          { createdAt: 'desc' },
        ],
      });

      return affordableBook;
    }

    return null;
  } catch (error) {
    console.error('Error getting book for rank:', error);
    return null;
  }
};

// Place automatic order for a specific user
export const placeAutomaticOrder = async (
  studentId: string,
  rank: number,
  reason: string = 'Leaderboard Winner Reward'
): Promise<OrderResult> => {
  try {
    console.log(`🎯 Placing automatic order for student ${studentId} (Rank ${rank})`);

    // Get configuration
    const config = await getAutoOrderConfig();

    // Check if user already received an automatic order today
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const existingOrder = await prisma.storeOrder.findFirst({
      where: {
        modelId: studentId,
        modelType: 'STUDENT',
        createdAt: { gte: startOfDay, lt: endOfDay },
        item: { category: 'Books' },
      },
    });

    if (existingOrder) {
      return {
        success: false,
        error: 'User already received an automatic book order today',
      };
    }

    // Check user's coin balance
    const userCoins = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId: studentId,
          modelType: 'STUDENT',
        },
      },
    });

    const availableCoins = userCoins?.coins || 0;
    if (availableCoins <= 0) {
      return {
        success: false,
        error: 'User has insufficient coins',
      };
    }

    // Get appropriate book for this rank
    const selectedBook = await getBookForRank(rank, availableCoins, config);
    if (!selectedBook) {
      return {
        success: false,
        error: 'No suitable book found for automatic order',
      };
    }

    // Prepare purchase data
    const purchaseData = {
      userId: studentId,
      userType: 'STUDENT' as const,
      cartItems: [
        {
          id: selectedBook.id,
          name: selectedBook.name,
          coinPrice: selectedBook.coinPrice,
          quantity: 1,
        },
      ],
      totalCoins: selectedBook.coinPrice,
    };

    // Process the purchase
    const result = await processPurchase(purchaseData);

    if (result.success) {
      // Log the automatic order for tracking
      console.log(`✅ Automatic order placed successfully:`);
      console.log(`   Student: ${studentId}`);
      console.log(`   Rank: ${rank}`);
      console.log(`   Book: ${selectedBook.name}`);
      console.log(`   Price: ${selectedBook.coinPrice} coins`);
      console.log(`   Order ID: ${result.orderId}`);
      console.log(`   Reason: ${reason}`);

      return {
        success: true,
        orderId: result.orderId,
        bookName: selectedBook.name,
        coinPrice: selectedBook.coinPrice,
      };
    } else {
      return {
        success: false,
        error: result.error || 'Failed to process purchase',
      };
    }
  } catch (error) {
    console.error('Error in placeAutomaticOrder:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Place automatic orders for multiple winners
export const placeAutomaticOrdersForWinners = async (
  winners: Array<{ studentId: string; rank: number; name?: string }>
): Promise<Array<OrderResult & { studentId: string; rank: number }>> => {
  console.log(`📚 Processing automatic orders for ${winners.length} winners...`);

  const results = await Promise.allSettled(
    winners.map(async (winner) => {
      const result = await placeAutomaticOrder(
        winner.studentId,
        winner.rank,
        `Daily Leaderboard Rank ${winner.rank} Reward`
      );
      return { ...result, studentId: winner.studentId, rank: winner.rank };
    })
  );

  const processedResults = results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      console.error(`Failed to process winner ${index + 1}:`, result.reason);
      return {
        success: false,
        error: result.reason instanceof Error ? result.reason.message : 'Unknown error',
        studentId: winners[index].studentId,
        rank: winners[index].rank,
      };
    }
  });

  const successCount = processedResults.filter(r => r.success).length;
  console.log(`📊 Automatic orders completed: ${successCount}/${winners.length} successful`);

  return processedResults;
};

// Get automatic order statistics
export const getAutomaticOrderStats = async (days: number = 30) => {
  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await prisma.storeOrder.groupBy({
      by: ['status'],
      where: {
        createdAt: { gte: startDate },
        item: { category: 'Books' },
        modelType: 'STUDENT',
      },
      _count: { id: true },
      _sum: { totalCoins: true },
    });

    const totalOrders = stats.reduce((sum, stat) => sum + stat._count.id, 0);
    const totalCoinsSpent = stats.reduce((sum, stat) => sum + (stat._sum.totalCoins || 0), 0);

    return {
      totalOrders,
      totalCoinsSpent,
      statusBreakdown: stats.map(stat => ({
        status: stat.status,
        count: stat._count.id,
        totalCoins: stat._sum.totalCoins || 0,
      })),
      period: `Last ${days} days`,
    };
  } catch (error) {
    console.error('Error getting automatic order stats:', error);
    return null;
  }
};
