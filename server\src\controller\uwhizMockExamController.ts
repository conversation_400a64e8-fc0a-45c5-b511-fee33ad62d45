import { Request, Response } from 'express';
import { getMockQuestionsForStudent } from '../services/uwhizMockExamService';
import { MEDIUM } from '@prisma/client';

export const getMockQuestions = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const medium = req.params.medium as MEDIUM;
    const questions = await getMockQuestionsForStudent(studentId, medium);
    res.status(200).json(questions);
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};