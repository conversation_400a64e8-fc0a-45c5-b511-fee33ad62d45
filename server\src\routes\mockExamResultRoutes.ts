import { Router } from 'express';
import { saveMockExamResultController, getMockExamCombinedController } from '../controller/mockExamResultController';
import { studentAuthMiddleware } from '../middleware/studentAuth';

 const mockExamResultRoutes = Router();

mockExamResultRoutes.get('/:studentId',studentAuthMiddleware, getMockExamCombinedController);
mockExamResultRoutes.post('/',studentAuthMiddleware, saveMockExamResultController);

export default mockExamResultRoutes;