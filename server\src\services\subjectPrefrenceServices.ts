import prisma from "../config/prismaClient";

// Add SubjectPrefrences 
export const addSubjectPrefrence = async (data: { examId: number; subject: string }) => {
    return prisma.$transaction(async (tx) => {
        const existingPreferences = await tx.subjectPrefrence.findMany({
            where: { examId: data.examId }
        });
        const totalCount = existingPreferences.length + 1;
        const newWeight = 100 / totalCount;

        const newPreference = await tx.subjectPrefrence.create({
            data: {
                examId: data.examId,
                subject: data.subject,
                weightage: newWeight,
            },
        });

        await Promise.all(
            existingPreferences.map((pref) =>
                tx.subjectPrefrence.update({
                    where: { id: pref.id },
                    data: { weightage: newWeight },
                })
            )
        );

        return newPreference;
    });
};

// Get all SubjectPreferences
export const getSubjectPrefrence = async (examId: number) => {
    return prisma.subjectPrefrence.findMany({ where: { examId } });
};

// Delete SubjectPreference 
export const deleteSubjectPrefrence = async (id: string) => {
    try {
        return await prisma.$transaction(async (tx) => {
            await tx.subjectPrefrence.delete({ where: { id } });

            const remainingPreferences = await tx.subjectPrefrence.findMany();

            if (remainingPreferences.length > 0) {
                const newWeight = 100 / remainingPreferences.length;
                await Promise.all(
                    remainingPreferences.map((pref) =>
                        tx.subjectPrefrence.update({
                            where: { id: pref.id },
                            data: { weightage: newWeight },
                        })
                    )
                );
            }
            return true;
        });
    } catch (error) {
        return false;
    }
};