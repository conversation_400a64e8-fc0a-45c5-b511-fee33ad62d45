import { Request, Response } from 'express';
import {
  createPriceRank,
  getPriceRanksByExamId,
  updatePriceRank,
  deletePriceRank
} from '../services/uwhizPriceRankServices';


export const createPriceRankController = async (req: Request, res: Response):Promise<any> => {
  try {
    const { examId, rank, price } = req.body;

    if (!examId || !rank || price === undefined) {
      return res.status(400).json({ error: 'examId, rank, and price are required' });
    }

    const newPriceRank = await createPriceRank(
      parseInt(examId),
      parseInt(rank),
      parseInt(price)
    );

    res.status(201).json(newPriceRank);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const getPriceRanksController = async (req: Request, res: Response):Promise<any> => {
  try {
    const { examId } = req.params;

    if (!examId) {
      return res.status(400).json({ error: 'examId is required' });
    }

    const priceRanks = await getPriceRanksByExamId(parseInt(examId));
    res.status(200).json(priceRanks);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const updatePriceRankController = async (req: Request, res: Response):Promise<any> => {
  try {
    const { id } = req.params;
    const { rank, price } = req.body;

    if (!id) {
      return res.status(400).json({error: 'id is required' });
    }

    const updateData: Partial<{ rank: number; price: number }> = {};
    if (rank !== undefined) {
      updateData.rank = parseInt(rank);
    }
    if (price !== undefined) {
      updateData.price = parseInt(price);
    }

    const updatedPriceRank = await updatePriceRank(id, updateData);

    res.status(200).json(updatedPriceRank);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};

export const deletePriceRankController = async (req: Request, res: Response):Promise<any> => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'id is required' });
    }

    await deletePriceRank(id);
    res.status(204).send();
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};