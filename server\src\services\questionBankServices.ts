import prisma from "../config/prismaClient";

//Create New Question
export const addNewQuestion = async(data:any)=>{
    return prisma.questionBank.create({data});
}

//get All Question
export const getAllQuestion = async (page: number = 1, limit: number = 10,
  filters:{
    subject?:string,
    medium?:string,
    standard?:string,
    level?:string,
}={}) => {
  const skip = (page - 1) * limit;

  const where: any = {};
  if (filters.medium) where.medium = filters.medium;
  if (filters.standard) where.standard = filters.standard;
  if (filters.level) where.level = filters.level;
  if (filters.subject) where.subject = filters.subject;


  const questions = await prisma.questionBank.findMany({
    where,
    skip: skip,
    take: limit,
    orderBy:{
      createdAt:"desc"
    }
  });

  const totalQuestions = await prisma.questionBank.count({where});

  return {
    questions,
    totalQuestions,
    totalPages: Math.ceil(totalQuestions / limit),
    currentPage: page,
  };
};

//update Question
export const updateQuestion = async(id:string,data:Partial<any>)=>{
    return prisma.questionBank.update({where:{id},data})
}

//Delete Question
export const deleteQuestion = async(id:string) =>{
    try {
        await prisma.questionBank.delete({where:{id}})
        return true;
    } catch (error) {
        return false
    }
}

export const bulkImportQuestions = async(questions: any[]) => {
    try {
        const questionsToImport = questions.map(q => ({
            question: q.question,
            optionOne: q.optionOne,
            optionTwo: q.optionTwo,
            optionThree: q.optionThree,
            optionFour: q.optionFour,
            correctAnswer: q.correctAnswer,
            medium: q.medium,
            standard: q.standard,
            subject: q.subject,
            level: q.level,
            classID: q.classID || null
        }));

        const result = await prisma.questionBank.createMany({
            data: questionsToImport,
            skipDuplicates: true
        });

        return {
            success: true,
            uploadedCount: result.count,
            totalReceived: questions.length,
            message: `Successfully imported ${result.count} out of ${questions.length} questions`
        };
    } catch (error: any) {
        throw new Error(`Failed to import questions: ${error.message}`);
    }
}

export const deleteManyQuestions = async (ids: string[]) => {
  try {
    const result = await prisma.questionBank.deleteMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
    return {
      success: true,
      deletedCount: result.count,
      message: `Successfully deleted ${result.count} questions`,
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete questions: ${error.message}`,
    };
  }
};