import { Router } from 'express';
import * as examController from '../controller/examController';
import { examSchema, examUpdateSchema } from '../validations/examValidation';
import { validateSchema } from '../middleware/validateSchema';
import { authClientMiddleware } from '../middleware/clientAuth';

const examRouter = Router();

examRouter.post('/',  authClientMiddleware,validateSchema(examSchema), examController.createExam);
examRouter.put('/:id', authClientMiddleware,validateSchema(examUpdateSchema), examController.updateExam);
examRouter.get('/', examController.getAllExams);
examRouter.get('/:id', examController.getExamById);
examRouter.delete('/:id', authClientMiddleware, examController.deleteExam);

export default examRouter;
