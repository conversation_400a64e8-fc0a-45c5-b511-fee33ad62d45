import prisma from "../config/prismaClient";
import axios from "axios";

export const applyForExam = async (examId: number, applicantId: string) => {
  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: {
      id: true,
      exam_name: true,
      coins_required: true,
      total_student_intake: true,
      exam_type: true,
    },
  });

  if (!exam) {
    throw new Error("Exam not found");
  }

  if (examId === 7) {
    const hasAppliedForExam1 = await prisma.examApplication.findFirst({
      where: {
        examId: 1,
        applicantId: applicantId,
      },
    });

    if (!hasAppliedForExam1) {
      throw new Error("This exam is only for those who have participated in the Uwhiz Super Kids Exam. You have not participated in that Exam.");
    }

    const hasAttemptedSuperKids = await prisma.saveExamAnswer.findFirst({
      where: {
        examId: 1,
        studentId: applicantId,
      },
    });


    if (!hasAttemptedSuperKids) {
      throw new Error("You must have attempted the Uwhiz Super Kids Exam to apply for this exam.");
    }
  }

  const existingApplication = await prisma.examApplication.findFirst({
    where: {
      examId,
      applicantId,
    },
  });

  if (existingApplication) {
    throw new Error("This applicant has already applied for the exam");
  }

  const joinedApplicantsCount = await prisma.examApplication.count({
    where: { examId },
  });

  if (joinedApplicantsCount >= exam.total_student_intake) {
    throw new Error(
      "Maximum number of applicants already reached for this exam"
    );
  }

  if (exam.coins_required && exam.coins_required > 0) {
    const modelType = exam.exam_type === 'CLASSES' ? 'CLASS' : 'STUDENT';

    try {
      const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhizCoinDeduction/validate-and-deduct`, {
        modelId: applicantId,
        modelType,
        coinsRequired: exam.coins_required,
      });

      if (!response.data.success) {
        throw new Error('Failed to deduct coins');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Error validating coins');
    }
  }

  // Insert record into ExamApplication
  const application = await prisma.examApplication.create({
    data: {
      examId,
      applicantId,
    },
  });

  return {
    application,
    joinedApplicantsCount: joinedApplicantsCount + 1,
  };
};
export const getApplicants = async (
  examId: any,
  page: any,
  limit: any,
  filters: {
    firstName?: string,
    lastName?: string,
    email?: string,
    contact?: string,
    search?: string
  } = {}
) => {
  const validLimits = [10, 20, 50, 100, 200, 500];
  const validatedLimit = validLimits.includes(parseInt(limit)) ? parseInt(limit) : 10;
  const currentPage = parseInt(page) || 1;
  const skip = (currentPage - 1) * validatedLimit;

  const exam = await prisma.exam.findUnique({
    where: { id: parseInt(examId) }
  });

  if (!exam) {
    throw new Error('Exam not found');
  }
  const allApplications = await prisma.examApplication.findMany({
    where: { examId: parseInt(examId) },
    orderBy: { createdAt: 'desc' },
  });

  if (allApplications.length === 0) {
    return {
      total: 0,
      totalPages: 0,
      currentPage,
      limit: validatedLimit,
      data: [],
    };
  }
  const applicantIds = allApplications.map(app => app.applicantId);

  try {
    const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhizApplicant/get-user-details`, {
      examType: exam.exam_type,
      applicantIds,
    });
    let combinedData = response.data.map((applicant: any) => {
      const application = allApplications.find(app => app.applicantId === applicant.id);
      return {
        ...applicant,
        createdAt: application?.createdAt || new Date(),
        applicationId: application?.id
      };
    });

    if (filters.firstName && filters.firstName.trim()) {
      combinedData = combinedData.filter((applicant: any) =>
        applicant.firstName?.toLowerCase().includes(filters.firstName!.toLowerCase().trim())
      );
    }

    if (filters.lastName && filters.lastName.trim()) {
      combinedData = combinedData.filter((applicant: any) =>
        applicant.lastName?.toLowerCase().includes(filters.lastName!.toLowerCase().trim())
      );
    }

    if (filters.email && filters.email.trim()) {
      combinedData = combinedData.filter((applicant: any) =>
        applicant.email?.toLowerCase().includes(filters.email!.toLowerCase().trim())
      );
    }

    if (filters.contact && filters.contact.trim()) {
      combinedData = combinedData.filter((applicant: any) =>
        applicant.contact?.toString().includes(filters.contact!.trim())
      );
    }

    if (filters.search && filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      combinedData = combinedData.filter((applicant: any) =>
        applicant.firstName?.toLowerCase().includes(searchTerm) ||
        applicant.lastName?.toLowerCase().includes(searchTerm) ||
        applicant.email?.toLowerCase().includes(searchTerm) ||
        applicant.contact?.toString().includes(searchTerm)
      );
    }
    const totalFiltered = combinedData.length;
    const totalPages = Math.ceil(totalFiltered / validatedLimit);
    const paginatedData = combinedData.slice(skip, skip + validatedLimit);

    return {
      total: totalFiltered,
      totalPages,
      currentPage,
      limit: validatedLimit,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
      data: paginatedData,
    };

  } catch (error) {
    console.error('Error fetching user details:', error);
    throw new Error('Failed to fetch applicant details');
  }
};
