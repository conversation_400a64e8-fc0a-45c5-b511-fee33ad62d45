import { LEVEL, MEDIUM, PrismaClient } from '@prisma/client';
import { QueryParams, QuestionResponse } from '@/utils/types/questionBankType';
import NodeCache from 'node-cache';

const quizCache = new NodeCache({ stdTTL: 3600, checkperiod: 120 });
const prisma = new PrismaClient();

interface QuizState {
  questions: QuestionResponse[];
  currentQuestionIndex: number;
  userAnswers: { questionId: string; selectedAnswer: string }[];
}

const getCacheKey = (studentId: string, examId: string) => `quiz:${studentId}:${examId}`;

export const getQuestionsForStudent = async (
  { standard, medium, studentId }: QueryParams,
  examId: string
): Promise<QuestionResponse[]> => {
  const normalizedMedium = medium.toUpperCase() as MEDIUM;
  const cacheKey = getCacheKey(studentId, examId);

  const cachedState = quizCache.get<QuizState>(cacheKey);
  if (cachedState) {
    return cachedState.questions; 
  }

  // Step 1: Fetch exam with preferences in one query
  const examApplication = await prisma.examApplication.findFirst({
    where: { applicantId: studentId, examId: Number(examId) },
    include: {
      exam: {
        select: {
          id: true,
          exam_name: true,
          total_questions: true,
          subjectPrefrence: { select: { subject: true, weightage: true } },
          levelPrefrence: { select: { level: true, weightage: true, timePerQuestion: true } },
        },
      },
    },
  });

  if (!examApplication || !examApplication.exam) {
    throw new Error('No exams found for this student');
  }

  const { exam } = examApplication;
  const { total_questions, subjectPrefrence, levelPrefrence, exam_name } = exam;

  // Step 2: Calculate question distribution based on weightages
  const totalSubjectWeight = subjectPrefrence.reduce((sum, sp) => sum + sp.weightage, 0);
  const totalLevelWeight = levelPrefrence.reduce((sum, lp) => sum + lp.weightage, 0);

  const subjectCounts: { [subject: string]: number } = {};
  const levelCounts: { [level: string]: number } = {};

  subjectPrefrence.forEach(sp => {
    subjectCounts[sp.subject] = Math.round((sp.weightage / totalSubjectWeight) * total_questions);
  });

  levelPrefrence.forEach(lp => {
    levelCounts[lp.level] = Math.round((lp.weightage / totalLevelWeight) * total_questions);
  });

  // Step 3: Fetch questions with database-level randomization
  const selectedQuestions: QuestionResponse[] = [];

  for (const sp of subjectPrefrence) {
    const subject = sp.subject;
    const questionCount = subjectCounts[subject] || 0;
    if (questionCount <= 0) continue;

    for (const lp of levelPrefrence) {
      const level = lp.level as LEVEL;
      const levelCount = levelCounts[lp.level] || 0;
      if (levelCount <= 0) continue;

      // Calculate how many questions to fetch for this subject-level combo
      const questionsToFetch = Math.min(
        Math.round((sp.weightage / totalSubjectWeight) * (lp.weightage / totalLevelWeight) * total_questions),
        questionCount,
        levelCount
      );

      if (questionsToFetch <= 0) continue;

      // Fetch questions with randomization at database level
      const questions = await prisma.questionBank.findMany({
        where: {
          standard,
          medium: normalizedMedium,
          subject,
          level,
        },
        take: questionsToFetch,
        orderBy: { id: 'asc' },
        select: {
          id: true,
          question: true,
          optionOne: true,
          optionTwo: true,
          optionThree: true,
          optionFour: true,
          subject: true,
          level: true
        }
      });

      questions.forEach(q => {
        selectedQuestions.push({
          id: q.id,
          question: q.question,
          optionOne: q.optionOne,
          optionTwo: q.optionTwo,
          optionThree: q.optionThree,
          optionFour: q.optionFour,
          subject: q.subject,
          level: q.level,
          timePerQuestion: lp.timePerQuestion,
          exam_name: exam_name
        });

        subjectCounts[subject]--;
        levelCounts[lp.level]--;
      });
    }
  }

  // Step 4: Shuffle if needed and ensure total_questions limit
  const shuffledQuestions = selectedQuestions.sort(() => Math.random() - 0.5).slice(0, total_questions);

  // Cache the initial quiz state
  const initialState: QuizState = {
    questions: shuffledQuestions,
    currentQuestionIndex: 0,
    userAnswers: [],
  };
  quizCache.set(cacheKey, initialState);

  return shuffledQuestions;
};

// Function to get quiz state
export const getQuizState = async (studentId: string, examId: string): Promise<QuizState | null> => {
  const cacheKey = getCacheKey(studentId, examId);
  return quizCache.get<QuizState>(cacheKey) || null;
};

// Function to update quiz state
export const updateQuizState = async (
  studentId: string,
  examId: string,
  state: Partial<QuizState>
): Promise<void> => {
  const cacheKey = getCacheKey(studentId, examId);
  const currentState = quizCache.get<QuizState>(cacheKey) || {
    questions: [],
    currentQuestionIndex: 0,
    userAnswers: [],
  };
  const updatedState = {
    ...currentState,
    ...state,
  };
  quizCache.set(cacheKey, updatedState);
};

// Function to clear quiz state
export const clearQuizState = async (studentId: string, examId: string): Promise<void> => {
  const cacheKey = getCacheKey(studentId, examId);
  quizCache.del(cacheKey);
};