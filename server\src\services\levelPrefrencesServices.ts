import prisma from "../config/prismaClient";

//Create New SubjectPrefrence
export const addLevelPrefrences = async (data: { examId: number; level: string; time?: number }) => {
    const levelTimeMap: { [key: string]: number } = {
        EASY: 30,
        MEDIUM: 45,
        HARD: 60,
    };

    const level = data.level?.toUpperCase();
    if (!level || !Object.keys(levelTimeMap).includes(level)) {
        throw new Error('Invalid or missing level. Must be EASY, MEDIUM, or HARD.');
    }

    const time = data.time || levelTimeMap[level];

    return prisma.$transaction(async (tx) => {
        const existingPreferences = await tx.levelPrefrences.findMany(
            { where: { examId: data.examId } }
        );

        const totalCount = existingPreferences.length + 1;
        const newWeight = 100 / totalCount;

        const newPreference = await tx.levelPrefrences.create({
            data: {
                examId: data.examId,
                level: data.level,
                timePerQuestion: time,
                weightage: newWeight,
            },
        });

        await Promise.all(
            existingPreferences.map((pref) =>
                tx.levelPrefrences.update({
                    where: { id: pref.id },
                    data: { weightage: newWeight },
                })
            )
        );

        return newPreference;
    });
};

//get Prefrence
export const getLevelPrefrence = async(examId:number) =>{
    return prisma.levelPrefrences.findMany({where: { examId }});
}


//delete prefrence
export const deleteLevelPrefrence = async (id:string)=>{
    try {
          return await prisma.$transaction(async (tx) => {
            await tx.levelPrefrences.delete({ where: { id } });

            const remainingPreferences = await tx.levelPrefrences.findMany();
            
            if (remainingPreferences.length > 0) {
                const newWeight = 100 / remainingPreferences.length;
                await Promise.all(
                    remainingPreferences.map((pref) =>
                        tx.levelPrefrences.update({
                            where: { id: pref.id },
                            data: { weightage: newWeight },
                        })
                    )
                );
            }
            return true;
        });
    } catch (error) {
        return false
    }
}