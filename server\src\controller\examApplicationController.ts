import { Request, Response } from 'express';
import * as examApplicationService from '../services/examApplicationServices';

// Apply for an exam
export const applyForExam = async (req: Request, res: Response): Promise<void> => {
  const { examId, applicantId } = req.body;

  if (!examId || !applicantId || isNaN(Number(examId))) {
    res.status(400).json({
      message: 'examId and applicantId are required, and examId must be a valid number',
    });
    return;
  }

  try {
    const { application, joinedApplicantsCount } = await examApplicationService.applyForExam(
      Number(examId),
      applicantId
    );
    res.status(201).json({
      message: 'Successfully applied for the exam',
      application,
      joinedApplicantsCount,
    });
  } catch (error: any) {
    res.status(400).json({
      message: 'Error applying for exam',
      error: error.message,
    });
  }
};
export const getApplicants = async (req: Request, res: Response): Promise<void> => {
  try {
    const { examId } = req.params;
    const { 
      page = 1, 
      limit = 10,
      firstName,
      lastName,
      email,
      contact,
      search
    } = req.query;
    if (!examId || isNaN(Number(examId))) {
      res.status(400).json({
        message: 'Valid examId is required',
      });
      return;
    }
    const filters: {
      firstName?: string;
      lastName?: string;
      email?: string;
      contact?: string;
      search?: string;
    } = {};

    if (firstName) filters.firstName = firstName as string;
    if (lastName) filters.lastName = lastName as string;
    if (email) filters.email = email as string;
    if (contact) filters.contact = contact as string;
    if (search) filters.search = search as string;

    const result = await examApplicationService.getApplicants(
      Number(examId),
      parseInt(page as string),
      parseInt(limit as string),
      filters
    );

    res.status(200).json({
      message: 'Applicants retrieved successfully',
      ...result,
    });
  } catch (error: any) {
    res.status(500).json({ 
      message: 'Failed to get applicants',
      error: error.message 
    });
  }
};