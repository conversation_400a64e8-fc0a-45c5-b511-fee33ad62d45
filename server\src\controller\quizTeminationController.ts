import { Request, Response } from "express";
import * as quizTermination from "../services/quizTerminationServices";

export const addQuizTeminationController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { examId, studentId, reason } = req.body;
    const data = await quizTermination.addQuizTerminationServices(
      examId,
      studentId,
      reason
    );
    res.status(201).json({
      message: "Applicant Data Added Into Termination Table",
      data: data,
    });
  } catch (error: any) {
    res.status(400).json({
      message: "Error saving termination records",
      error: error.message,
    });
  }
};

export const getTerminatedStudentsController = async (req:Request, res:Response) => {
  try {
    const { examId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const result = await quizTermination.getQuizTerminationServices(examId, parseInt(page as string), parseInt(limit as string));
    res.json(result);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to get students' });
  }
};
export const getStudentTerminationLogsController = async (req: Request, res: Response) => {
  try {
    const { examId, studentId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const result = await quizTermination.getStudentTerminationLogs(
      parseInt(examId), 
      studentId,
      parseInt(page as string),
      parseInt(limit as string)
    );
      res.json(result);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to get students Terminationlog' });
  
  }
};