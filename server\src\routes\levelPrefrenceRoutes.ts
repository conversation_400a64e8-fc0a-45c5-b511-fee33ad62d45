import {Router} from 'express'
import {addLevelPrefrenceController,deleteLevelPrefrences,getLevelPrefrencesController} from '../controller/levelPrefrencesController'
import { authClientMiddleware } from '../middleware/clientAuth';

const levelPrefrenceRoutes = Router();

levelPrefrenceRoutes.get('/:examId',authClientMiddleware,getLevelPrefrencesController);
levelPrefrenceRoutes.post('/',authClientMiddleware, addLevelPrefrenceController);
levelPrefrenceRoutes.delete('/:id',authClientMiddleware,deleteLevelPrefrences);

export default levelPrefrenceRoutes;