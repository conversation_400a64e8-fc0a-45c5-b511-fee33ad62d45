import { Request, Response } from 'express';
import { getLeaderboard } from '../services/mockExamLeaderBoardService';

export const getetLeaderboardController = async (req: Request, res: Response):Promise<any> => {
    try {
      const timeframe = req.params.timeframe as any;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      if (!['today', 'weekly', 'all-time'].includes(timeframe)) {
        return res.status(400).json({ error: 'Invalid timeframe' });
      }

      const leaderboard = await getLeaderboard(timeframe, page, limit);
      return res.status(200).json(leaderboard);
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
}
