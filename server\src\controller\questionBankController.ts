import { Request, Response } from "express";
import * as questionBankServices from "../services/questionBankServices";
import * as XLSX from 'xlsx';
import multer from 'multer';

export const createNewQuestion = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const data = req.body;
    const questionBankData = await questionBankServices.addNewQuestion(data);
    res.status(201).json({success:true, data:questionBankData});
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const getAllQuestions = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const { medium, standard, level, subject } = req.query;

     const filters: {
      medium?: string;
      standard?: string;
      level?: string;
      subject?: string;
    } = {};

    if (typeof medium === "string") filters.medium = medium;
    if (typeof standard === "string") filters.standard = standard;
    if (typeof level === "string") filters.level = level;
    if (typeof subject === "string") filters.subject = subject;

    const questionBankData = await questionBankServices.getAllQuestion(page, limit,filters);

    res.status(200).json({
      success: true,
      data: questionBankData.questions,
      pagination: {
        totalQuestions: questionBankData.totalQuestions,
        totalPages: questionBankData.totalPages,
        currentPage: questionBankData.currentPage,
        limit: limit,
      },
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const updateQuestion = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const data = req.body;
    const updatedData = await questionBankServices.updateQuestion(id, data);
    res.status(200).json({success:true,data:updatedData});
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const deleteQuestions = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const success = await questionBankServices.deleteQuestion(id);
    if (!success) res.status(404).send("question Not found");
    res.status(204).send();
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const handleUploadExcel = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: "No file uploaded"
      });
      return;
    }

    const workbook = XLSX.read(req.file.buffer, { type: "buffer" });
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(sheet);

    if (!data || data.length === 0) {
      res.status(400).json({ error: "Excel file is empty or invalid" });
      return;
    }

    const validQuestions: any[] = [];
    const skippedQuestions: any[] = [];

    data.forEach((row: any, index: number) => {
      const question = {
        question: row.question || row.Question || '',
        optionOne: row.optionOne || row.OptionOne || row['Option 1'] || '',
        optionTwo: row.optionTwo || row.OptionTwo || row['Option 2'] || '',
        optionThree: row.optionThree || row.OptionThree || row['Option 3'] || '',
        optionFour: row.optionFour || row.OptionFour || row['Option 4'] || '',
        correctAnswer: row.correctAnswer || row.CorrectAnswer || row['Correct Answer'] || '',
        medium: row.medium || row.Medium || '',
        standard: row.standard || row.Standard || '',
        subject: row.subject || row.Subject || '',
        level: row.level || row.Level || '',
        classID: row.classID || row.ClassID || row['Class ID'] || null,
      };

      const validLevels = ['EASY', 'MEDIUM', 'HARD'];
      const validMediums = ['GUJARATI', 'ENGLISH'];
      const isValidLevel = question.level && validLevels.includes(question.level.toUpperCase());
      const isValidMedium = question.medium && validMediums.includes(question.medium.toUpperCase());
      const isValidQuestion = question.question &&
        question.optionOne &&
        question.optionTwo &&
        question.optionThree &&
        question.optionFour &&
        question.correctAnswer &&
        question.medium &&
        question.standard &&
        question.subject &&
        question.level &&
        isValidLevel &&
        isValidMedium;

      if (isValidQuestion) {
        validQuestions.push({
          ...question,
          level: question.level.toUpperCase(), 
          medium: question.medium.toUpperCase() 
        });
      } else {
        let reason = 'One or more required fields are empty';
        if (question.level && !isValidLevel) {
          reason = `Invalid level "${question.level}". Must be EASY, MEDIUM, or HARD`;
        } else if (question.medium && !isValidMedium) {
          reason = `Invalid medium "${question.medium}". Must be GUJARATI or ENGLISH`;
        }
        skippedQuestions.push({
          row: index + 2,
          question: question.question || 'Empty question',
          reason: reason
        });
      }
    });

    const result = await questionBankServices.bulkImportQuestions(validQuestions);

    res.status(200).json({
      message: "Excel import completed",
      totalRows: data.length,
      validQuestions: validQuestions.length,
      skippedQuestions: skippedQuestions.length,
      uploadedCount: result.uploadedCount,
      skippedQuestionsSample: skippedQuestions.slice(0, 5),
    });

  } catch (error: any) {
    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        res.status(400).json({
          success: false,
          error: 'Unexpected field name. Use "excelFile" as the field name.'
        });
        return;
      }
      if (error.code === 'LIMIT_FILE_SIZE') {
        res.status(400).json({
          success: false,
          error: 'File too large. Maximum size allowed is 3MB.'
        });
        return;
      }
    }

    res.status(500).json({
      success: false,
      error: "Failed to import Excel file: " + (error.response?.data?.message || error.message)
    });
  }
};


export const deleteManyQuestions = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or empty IDs array provided',
      });
    }

    const result = await questionBankServices.deleteManyQuestions(ids);

    if (result.success) {
      if (result.deletedCount === 0) {
        return res.status(404).json({
          success: false,
          error: 'No questions found with the provided IDs',
        });
      }
      return res.status(200).json({
        success: true,
        data: result,
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
  } catch (error: any) {
    return res.status(400).json({
      success: false,
      error: error.message || 'Failed to delete questions',
    });
  }
};