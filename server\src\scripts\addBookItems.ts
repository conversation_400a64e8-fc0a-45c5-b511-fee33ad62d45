import prisma from '@/config/prismaClient';

interface BookData {
  name: string;
  description: string;
  coinPrice: number;
  totalStock: number;
  category: string;
}

const bookItems: BookData[] = [
  {
    name: "Mathematics Fundamentals - Class 10",
    description: "Comprehensive guide covering all Class 10 mathematics topics with solved examples and practice questions.",
    coinPrice: 150,
    totalStock: 50,
    category: "Books"
  },
  {
    name: "Science Explorer - Physics & Chemistry",
    description: "Interactive science book covering basic physics and chemistry concepts with experiments and illustrations.",
    coinPrice: 200,
    totalStock: 40,
    category: "Books"
  },
  {
    name: "English Grammar & Composition",
    description: "Complete English grammar guide with composition writing techniques and vocabulary building exercises.",
    coinPrice: 120,
    totalStock: 60,
    category: "Books"
  },
  {
    name: "History & Civics - Indian Heritage",
    description: "Detailed study of Indian history and civics with maps, timelines, and important events.",
    coinPrice: 180,
    totalStock: 35,
    category: "Books"
  },
  {
    name: "Geography Atlas - World & India",
    description: "Comprehensive atlas with detailed maps of India and the world, including physical and political features.",
    coinPrice: 250,
    totalStock: 25,
    category: "Books"
  },
  {
    name: "Computer Science Basics",
    description: "Introduction to computer science concepts, programming basics, and digital literacy.",
    coinPrice: 300,
    totalStock: 30,
    category: "Books"
  },
  {
    name: "Environmental Studies",
    description: "Understanding our environment, conservation, and sustainable development practices.",
    coinPrice: 100,
    totalStock: 45,
    category: "Books"
  },
  {
    name: "Mental Math & Vedic Mathematics",
    description: "Learn quick calculation techniques and Vedic mathematics for faster problem solving.",
    coinPrice: 160,
    totalStock: 40,
    category: "Books"
  },
  {
    name: "Creative Writing & Poetry",
    description: "Develop creative writing skills with poetry, short stories, and essay writing techniques.",
    coinPrice: 140,
    totalStock: 35,
    category: "Books"
  },
  {
    name: "General Knowledge Encyclopedia",
    description: "Comprehensive collection of general knowledge covering science, history, geography, and current affairs.",
    coinPrice: 220,
    totalStock: 50,
    category: "Books"
  },
  {
    name: "Study Skills & Time Management",
    description: "Essential guide for effective studying, time management, and exam preparation strategies.",
    coinPrice: 90,
    totalStock: 55,
    category: "Books"
  },
  {
    name: "Art & Craft Activity Book",
    description: "Creative art and craft projects with step-by-step instructions and material lists.",
    coinPrice: 110,
    totalStock: 40,
    category: "Books"
  }
];

async function addBookItems() {
  try {
    console.log('🚀 Starting to add book items to the store...');

    // Check if Books category already has items
    const existingBooks = await prisma.storeItem.findMany({
      where: { category: 'Books' }
    });

    if (existingBooks.length > 0) {
      console.log(`📚 Found ${existingBooks.length} existing book items in store`);
      console.log('Skipping book creation to avoid duplicates');
      return;
    }

    // Add each book item
    const createdBooks = [];
    for (const book of bookItems) {
      try {
        const createdBook = await prisma.storeItem.create({
          data: {
            ...book,
            availableStock: book.totalStock,
            status: 'ACTIVE'
          }
        });
        createdBooks.push(createdBook);
        console.log(`✅ Added: ${book.name} (${book.coinPrice} coins)`);
      } catch (error) {
        console.error(`❌ Failed to add ${book.name}:`, error);
      }
    }

    console.log(`\n🎉 Successfully added ${createdBooks.length} book items to the store!`);
    console.log('\nBook Summary:');
    createdBooks.forEach((book, index) => {
      console.log(`${index + 1}. ${book.name} - ${book.coinPrice} coins (Stock: ${book.availableStock})`);
    });

    // Display price range
    const prices = createdBooks.map(book => book.coinPrice);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    console.log(`\n💰 Price Range: ${minPrice} - ${maxPrice} coins`);

  } catch (error) {
    console.error('❌ Error adding book items:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  addBookItems();
}

export { addBookItems, bookItems };
