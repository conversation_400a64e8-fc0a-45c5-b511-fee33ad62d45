import prisma from "../config/prismaClient";
import * as XLSX from "xlsx";

//create New Mock Exam
export const createNewMockExam = async (data: any) => {
    return await prisma.mockExamQuestionBank.create({data});
};

//get All Mock Exams
export const getAllMockExamQuestion = async (page: number = 1, limit: number = 10) => {
  const skip = (page - 1) * limit;


  const questions = await prisma.mockExamQuestionBank.findMany({
    skip: skip,
    take: limit,
    orderBy:{
      createdAt:"desc"
    }
  });

  const totalQuestions = await prisma.mockExamQuestionBank.count();

  return {
    questions,
    totalQuestions,
    totalPages: Math.ceil(totalQuestions / limit),
    currentPage: page,
  };
};

//update Mock exam
export const updateMockExam = async (id: string, data: Partial<any>) => {
    return await prisma.mockExamQuestionBank.update({where:{id},data});
}

//Delete Mock Exam
export const deleteMockExam = async (id: string) => {
    try {
        await prisma.mockExamQuestionBank.delete({where:{id}})
        return true;
    } catch (error) {
        return false
    }
}

export const deleteManyMockQuestions = async (ids: string[]) => {
  try {
    const result = await prisma.mockExamQuestionBank.deleteMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
    return {
      success: true,
      deletedCount: result.count,
      message: `Successfully deleted ${result.count} questions`,
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete questions: ${error.message}`,
    };
  }
};


export const importMockExamQuestionsFromExcel = async (buffer: Buffer) => {
  try {
    const workbook = XLSX.read(buffer, { type: "buffer" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    const validCorrectAnswers = ["optionOne", "optionTwo", "optionThree", "optionFour"];
    const questionsToCreate = jsonData
      .map((row: any, index: number) => {
        const question = {
          question: row.Question?.toString().trim(),
          optionOne: row["Option 1"]?.toString().trim(),
          optionTwo: row["Option 2"]?.toString().trim(),
          optionThree: row["Option 3"]?.toString().trim(),
          optionFour: row["Option 4"]?.toString().trim(),
          correctAnswer: row["Correct Answer"]?.toString().trim(),
          quetionDate: new Date(row["Question Date Answer"]?.toString().trim())
        };

        // Validation
        if (
          !question.question ||
          !question.optionOne ||
          !question.optionTwo ||
          !question.optionThree ||
          !question.optionFour ||
          !question.correctAnswer
        ) {
          throw new Error(`Row ${index + 2}: Missing required fields`);
        }

        if (!validCorrectAnswers.includes(question.correctAnswer)) {
          throw new Error(
            `Row ${index + 2}: Invalid correct answer. Must be one of ${validCorrectAnswers.join(", ")}`
          );
        }

        return question;
      });

    // Create questions in a transaction
    const createdQuestions = await prisma.$transaction(
      questionsToCreate.map((question) =>
        prisma.mockExamQuestionBank.create({ data: question })
      )
    );

    return {
      success: true,
      createdCount: createdQuestions.length,
      message: `Successfully imported ${createdQuestions.length} questions`,
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to import questions: ${error.message}`,
    };
  }
};