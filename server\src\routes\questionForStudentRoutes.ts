import { Router } from 'express';
import { getQuestions, getQuizStateController, updateQuizStateController, clearQuizStateController } from '../controller/uwhizQuestionForStudentController';

export const questionForStudentsRoutes = Router();

questionForStudentsRoutes.get('/', getQuestions);
questionForStudentsRoutes.get('/state', getQuizStateController);
questionForStudentsRoutes.post('/state', updateQuizStateController);
questionForStudentsRoutes.delete('/state', clearQuizStateController);