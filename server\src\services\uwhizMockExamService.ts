import { MEDIUM, PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
import NodeCache from 'node-cache';
import { toZonedTime } from 'date-fns-tz';

const quizCache = new NodeCache({ stdTTL: 86400, checkperiod: 60 });

export const getMockQuestionsForStudent = async (studentId: string, medium: MEDIUM): Promise<any[]> => {  
    await prisma.mockExamTermination.deleteMany({where:{studentId}})   

    const mockExamQuestions = await prisma.mockExamQuestionBank.findMany({
        select: {
            id: true,
            question: true,
            optionOne: true,
            optionTwo: true,
            optionThree: true,
            optionFour: true,
            correctAnswer: true,
        },
        where: {
            medium,
        },
    });

    const shuffledQuestions = mockExamQuestions.sort(() => Math.random() - 0.5).slice(0, 10);

    return shuffledQuestions;
} 