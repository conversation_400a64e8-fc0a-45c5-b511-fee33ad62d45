import { Request, Response } from "express";
import { addQuizTerminationServices,countTerminations } from "../services/uwhizMockExamTerminateServices";

export const addQuizTeminationController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { studentId, reason } = req.body;
    const data = await addQuizTerminationServices(
      studentId,
      reason
    );
    res.status(201).json({
      message: "Applicant Data Added Into Termination Table",
      data: data,
    });
  } catch (error: any) {
    res.status(400).json({
      message: "Error saving termination records",
      error: error.message,
    });
  }
};

export const countTerminationsController = async (req:Request, res:Response):Promise<any> => {
  try {
    const { studentId } = req.query;

    if (!studentId) {
      return res.status(400).json(false);
    }

    const terminationCount = await countTerminations(studentId);

    return res.status(200).json(terminationCount);
  } catch (error) {
    return res.status(500).json(false);
  }
};
