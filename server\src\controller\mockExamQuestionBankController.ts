import { Request, Response } from 'express';
import { createNewMockExam, deleteManyMockQuestions, deleteMockExam, getAllMockExamQuestion, importMockExamQuestionsFromExcel, updateMockExam } from '../services/mockExamQuestionBankService';


export const createNewMockExamController = async (req: Request, res: Response): Promise<void> => {
  try{
    const data = req.body;
    const mockExamQuestionBankData = await createNewMockExam(data);
    res.status(201).json({success:true, data:mockExamQuestionBankData});
  }catch{
    res.status(400).json({ success:false ,error: "Failed to create new mock exam" });
  }
}

export const getAllMockExamQuestionController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const mockExamQuestionBankData = await getAllMockExamQuestion(page, limit);

    res.status(200).json({
      success: true,
      data: mockExamQuestionBankData.questions,
      pagination: {
        totalQuestions: mockExamQuestionBankData.totalQuestions,
        totalPages: mockExamQuestionBankData.totalPages,
        currentPage: mockExamQuestionBankData.currentPage,
        limit: limit,
      },
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};


export const updateMockExamController = async (req: Request, res: Response): Promise<any> => {
  try {
    const id = req.params.id;
    const data = req.body;
    const updatedData = await updateMockExam(id, data);
    res.status(200).json({success:true,data:updatedData});
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const deleteMockExamController = async (req: Request, res: Response) => {
  try {
    const id = req.params.id;
    const success = await deleteMockExam(id);
    if (!success) res.status(404).send("mock exam Not found");
    res.status(204).send();
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const deleteManyMockExamController = async (req: Request, res: Response) => {
  try {
    const ids = req.body.ids;
    const success = await deleteManyMockQuestions(ids);
    if (!success) res.status(404).send("mock exam Not found");
    res.status(204).send();
  } catch (error:any) {
    res.status(400).json({ success:false ,error: error.message || error });
  }
};

export const importExcelController =  async (req: Request, res: Response):Promise<any> => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: "No file uploaded",
        });
      }

      const result = await importMockExamQuestionsFromExcel(req.file.buffer);
      if (result.success) {
        res.status(201).json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: `Server error: ${error.message}`,
      });
    }
  }