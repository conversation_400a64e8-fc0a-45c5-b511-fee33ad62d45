import { Request, Response } from "express";
import * as levelPrefrencesServices from "../services/levelPrefrencesServices";

export const addLevelPrefrenceController = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const data = req.body;
    const subjectPrefrenceData =
      await levelPrefrencesServices.addLevelPrefrences(data);
    res.status(201).json({ success: true, data: subjectPrefrenceData });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const getLevelPrefrencesController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const examId = parseInt(req.params.examId, 10);
    const subjectPrefrenceData =
      await levelPrefrencesServices.getLevelPrefrence(examId);
    return res.status(200).json({
      success: true,
      data: subjectPrefrenceData,
    });
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};

export const deleteLevelPrefrences = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const success = await levelPrefrencesServices.deleteLevelPrefrence(id);
    if (!success) res.status(404).send("LevelPrefrence Not found");
    res.status(204).send();
  } catch (error: any) {
    res.status(400).json({ success: false, error: error.message || error });
  }
};
