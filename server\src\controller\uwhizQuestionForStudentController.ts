import { Request, Response } from 'express';
import { getQuestionsForStudent, getQuizState, updateQuizState, clearQuizState } from '../services/getQuestionsForStudentServices';
import { QueryParams } from '@/utils/types/questionBankType';

export const getQuestions = async (req: Request, res: Response): Promise<any> => {
  try {
    const { standard, medium, studentId, examId } = req.query as unknown as QueryParams & { examId: string };

    if (!standard || !medium || !studentId || !examId) {
      return res.status(400).json({ error: 'Missing required query parameters: standard, medium, studentId, examId' });
    }

    const questions = await getQuestionsForStudent({ standard, medium, studentId }, examId);
    res.status(200).json(questions);
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};

export const getQuizStateController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId, examId } = req.query as { studentId: string; examId: string };

    if (!studentId || !examId) {
      return res.status(400).json({ error: 'Missing required query parameters: studentId, examId' });
    }

    const state = await getQuizState(studentId, examId);
    res.status(200).json(state);
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};

export const updateQuizStateController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId, examId } = req.query as { studentId: string; examId: string };
    const state = req.body;

    if (!studentId || !examId || !state) {
      return res.status(400).json({ error: 'Missing required parameters: studentId, examId, or state' });
    }

    await updateQuizState(studentId, examId, state);
    res.status(200).json({ success: true });
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};

export const clearQuizStateController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId, examId } = req.query as { studentId: string; examId: string };

    if (!studentId || !examId) {
      return res.status(400).json({ error: 'Missing required query parameters: studentId, examId' });
    }

    await clearQuizState(studentId, examId);
    res.status(200).json({ success: true });
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};